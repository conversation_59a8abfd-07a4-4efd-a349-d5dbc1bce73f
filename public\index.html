<!DOCTYPE html>
<html lang="de">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XennyGames</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(120deg, #181c2f 0%, #232526 50%, #414345 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 20px;
        }

        h1 {
            color: white;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            margin: 0;
        }

        .twitch-login-btn {
            background: linear-gradient(45deg, #9146ff, #772ce8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(145, 70, 255, 0.3);
        }

        .twitch-login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(145, 70, 255, 0.4);
            background: linear-gradient(45deg, #a970ff, #8b44f0);
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.15);
            padding: 10px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .user-name {
            color: white;
            font-weight: 600;
            font-size: 1rem;
            margin-left: auto;
        }

        .admin-badge {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 700;
            box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .admin-panel {
            display: none;
            margin-bottom: 20px;
            background: rgba(0, 0, 0, 0.9);
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .admin-panel h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .admin-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .admin-controls input {
            background-color: #222;    
            color: white;
            flex: 1;
            min-width: 200px;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .action-btn {
            background-color: #222; 
            color: #fff;            
            border: 1px solid #555;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .action-btn:hover {
            background: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .action-btn.spieleliste {
            border-color: #2196F3;
            color: #2196F3;
        }

        .action-btn.spieleliste:hover {
            background: #2196F3;
            color: white;
        }
        .boxart-category-select {
            border-radius: 6px;
            background-color: #222; 
            color: #fff;            
            border: 1px solid #555; 
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .game-box {
            background: rgba(12, 12, 12, 0.95);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .box-header {
            text-align: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 3px solid;
        }

        .gespielt .box-header {
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .wird-gespielt .box-header {
            border-color: #FF9800;
            color: #FF9800;
        }

        .spieleliste .box-header {
            border-color: #2196F3;
            color: #2196F3;
        }

        .game-count {
            font-size: 0.9rem;
            opacity: 0.7;
        }

        .game-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            padding: 10px;
            min-height: 300px;
            max-height: 600px;
            overflow-y: auto;
            scrollbar-color: #555 #1e1e1e;
            scrollbar-width: thin;
        }

        .game-container::-webkit-scrollbar {
            width: 8px;
        }

        .game-container::-webkit-scrollbar-track {
            background: #1e1e1e;
        }

        .game-container::-webkit-scrollbar-thumb {
            background-color: #555;
            border-radius: 4px;
            border: 2px solid #1e1e1e;
        }

        .game-boxart {
            background: rgba(0, 0, 0, 0.9);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .game-boxart:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        .boxart-image {
            width: 100%;
            height: 240px;
            object-fit: cover;
            border-bottom: 1px solid #eee;
            background: #f0f0f0;
        }

        .boxart-footer {
            padding: 10px;
            display: grid;
            grid-template-rows: auto auto auto;
            gap: 8px;
        }

        .boxart-name {
            font-weight: bold;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #eee;
        }

        .boxart-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            min-height: 24px;
        }

        .boxart-status {
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 11px;
        }

        .status-owned {
            background: #4CAF50;
            color: white;
        }

        .status-not-owned {
            background: #f44336;
            color: white;
        }

        .status-date {
            background: #2196F3;
            color: white;
        }

        .boxart-votes {
            display: flex;
            align-items: center;
            gap: 5px;
            min-height: 24px;
            flex-wrap: wrap;
        }

        .boxart-vote-btn {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            padding: 2px;
            transition: all 0.2s ease;
            color: grey;
        }

        .boxart-vote-btn:hover {
            transform: scale(1.1);
        }

        .boxart-vote-btn.upvote.active {
            color: #ff4500;
        }

        .boxart-vote-btn.downvote.active {
            color: #7193ff;
        }

        .boxart-score {
            font-size: 12px;
            font-weight: bold;
            min-width: 20px;
            text-align: center;
        }

        .score-positive {
            color: #ff4500;
        }

        .score-negative {
            color: #7193ff;
        }

        .score-zero {
            color: #878a8c;
        }

        .igdb-search-results {
            display: none;
            margin-top: 15px;
        }

        .igdb-search-results h4 {
            margin-bottom: 10px;
            color: #333;
        }

        .igdb-results-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
        }

        .igdb-result {
            background: white;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .igdb-result:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .igdb-result img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            background: #f0f0f0;
        }

        .igdb-result div {
            padding: 5px;
        }

        .igdb-result button {
            margin-top: 5px;
            width: 100%;
            padding: 3px;
            font-size: 11px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .game-boxart.dragging {
            opacity: 0.5;
            transform: scale(0.95);
            border: 2px dashed #2196F3;
        }

        .game-container.drop-target {
            background: rgba(33, 150, 243, 0.1);
            border: 2px dashed #2196F3;
        }

        .game-container.drop-target::after {
            content: "Spiel hier ablegen";
            display: block;
            text-align: center;
            color: #2196F3;
            padding: 10px;
        }

        .loading {
            text-align: center;
            color: white;
            font-size: 1.2rem;
            margin: 50px 0;
        }

        .error {
            background: rgba(244, 67, 54, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
            display: none;
        }

        .error h3 {
            margin-bottom: 10px;
        }

        .refresh-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 20px auto 0;
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .connection-status {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 5px 10px;
            border-radius: 15px;
            color: white;
            font-size: 14px;
            z-index: 1000;
        }
        
        .connection-status.connected {
            background: rgba(76, 175, 80, 0.7);
        }
        
        .connection-status.disconnected {
            background: rgba(244, 67, 54, 0.7);
        }

        @media (max-width: 768px) {
            .game-container {
                max-height: none;
                overflow-y: visible;
                padding: 5px;
            }

            .dashboard {
                grid-template-columns: 1fr;
                display: flex;
                flex-direction: column;
            }

            .game-box.spieleliste {
                order: -1;
            }

            .game-box.wird-gespielt {
                order: 0;
            }

            .game-box.gespielt {
                order: 1;
            }

            .boxart-image {
                height: 160px;
            }

            .header {
                flex-direction: column;
                text-align: center;
            }

            .user-section {
                flex-direction: column;
                gap: 10px;
            }
        }

        @media (max-width: 480px) {
            .game-boxart {
                font-size: 12px;
            }

            .boxart-footer {
                gap: 4px;
                padding: 8px;
            }

            .boxart-name {
                font-size: 12px;
            }

            .boxart-meta {
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                gap: 8px;
                min-height: 24px;
            }

            .boxart-votes {
                flex-shrink: 0;
            }

            .playlist-link {
                font-size: 10px;
                padding: 2px 4px;
                min-height: 18px;
            }

            .youtube-logo {
                width: 14px;
                height: 14px;
            }
        }

        .boxart-actions {
            display: grid;
            gap: 5px;
            margin-top: 5px;
        }

        .boxart-actions button {
            padding: 5px;
            font-size: 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .boxart-actions button:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .boxart-vote-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .game-box:not(.spieleliste) .boxart-vote-btn {
            display: none;
        }

        .playlist-section {
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .playlist-link {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            color: #ff0000;
            text-decoration: none;
            font-size: 11px;
            padding: 3px 6px;
            border-radius: 4px;
            background: rgba(255, 0, 0, 0.1);
            transition: all 0.2s ease;
            flex-shrink: 0;
            min-height: 20px;
        }

        .playlist-link:hover {
            background: rgba(255, 0, 0, 0.2);
            transform: translateY(-1px);
        }

        .youtube-logo {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .playlist-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .playlist-count {
            font-size: 10px;
            opacity: 0.8;
        }

        .playlist-input {
            width: 100%;
            padding: 4px 6px;
            font-size: 11px;
            border: 1px solid #555;
            border-radius: 3px;
            background: #222;
            color: white;
            margin-bottom: 4px;
        }

        .playlist-actions {
            display: flex;
            gap: 4px;
        }

        .playlist-btn {
            padding: 3px 6px;
            font-size: 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .playlist-btn.save {
            background: #4CAF50;
            color: white;
        }

        .playlist-btn.cancel {
            background: #f44336;
            color: white;
        }

        .playlist-btn.edit {
            background: #2196F3;
            color: white;
        }

        .playlist-btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🎮 XennyGames</h1>
            <div class="auth-section">
                <div id="login-section" class="login-section">
                    <button id="twitch-login-btn" class="twitch-login-btn">
                        🎮 Mit Twitch anmelden
                    </button>
                </div>
                <div id="user-section" class="user-section" style="display: none;">
                    <div class="user-info">
                        <img id="user-avatar" src="" alt="Avatar" class="user-avatar">
                        <span id="user-name" class="user-name"></span>
                        <span id="admin-badge" class="admin-badge" style="display: none;">👑 Admin</span>
                    </div>
                    <button id="logout-btn" class="logout-btn">Abmelden</button>
                </div>
            </div>
        </div>

        <div id="admin-panel" class="admin-panel">
            <h3>Admin Tools</h3>
            <div class="admin-controls">
                <input type="text" id="new-game-name" placeholder="Spielname">
                <button type="button" onclick="searchIGDB()" class="action-btn spieleliste">IGDB Suche</button>
                <button type="button" onclick="addGame()" class="action-btn spieleliste">Manuell hinzufügen</button>

                <button type="button" onclick="syncWithConfigServer()" class="action-btn" style="background: #9c27b0; color: white;">📡 Config Sync</button>
            </div>
            <div id="igdb-search-results" class="igdb-search-results">
                <h4>IGDB Suchergebnisse</h4>
                <div id="igdb-results-container" class="igdb-results-container"></div>
            </div>
        </div>

        <div id="loading" class="loading">
            Lade Spiele...
        </div>

        <div id="error" class="error">
            <h3>Fehler beim Laden der Spiele</h3>
            <p id="error-message"></p>
            <button class="refresh-btn" onclick="loadGames()">Erneut versuchen</button>
        </div>

        <div id="dashboard" class="dashboard" style="display: none;">
            <div class="game-box gespielt" data-category="Gespielt">
                <div class="box-header">
                    <h2>✅ Gespielt</h2>
                    <div class="game-count" id="gespielt-count">0 Spiele</div>
                </div>
                <div id="gespielt-games" class="game-container"></div>
            </div>

            <div class="game-box wird-gespielt" data-category="Spielt gerade">
                <div class="box-header">
                    <h2>🎯 Wird gespielt</h2>
                    <div class="game-count" id="wird-gespielt-count">0 Spiele</div>
                </div>
                <div id="wird-gespielt-games" class="game-container"></div>
            </div>

            <div class="game-box spieleliste" data-category="Auf der Spieleliste">
                <div class="box-header">
                    <h2>📋 Auf der Spieleliste</h2>
                    <div class="game-count" id="spieleliste-count">0 Spiele</div>
                </div>
                <div id="spieleliste-games" class="game-container"></div>
            </div>
        </div>
    </div>    
    <div id="connection-status" class="connection-status disconnected">Verbindung getrennt</div>
    <script src="/socket.io/socket.io.js"></script>
    <script>
        // Global variables
        let currentUser = null;
        let gameData = null;
        let isAdmin = false;
        let socket = null;
        let reconnectAttempts = 0;

        const MAX_RECONNECT_ATTEMPTS = 5;
        const RECONNECT_DELAY = 3000;

        // DOM elements
        const loginSection = document.getElementById('login-section');
        const userSection = document.getElementById('user-section');
        const userAvatar = document.getElementById('user-avatar');
        const userName = document.getElementById('user-name');
        const adminBadge = document.getElementById('admin-badge');
        const logoutBtn = document.getElementById('logout-btn');
        const adminPanel = document.getElementById('admin-panel');
        const dashboard = document.getElementById('dashboard');
        const loading = document.getElementById('loading');
        const error = document.getElementById('error');
        const errorMessage = document.getElementById('error-message');
        const connectionStatus = document.getElementById('connection-status');
        const twitchLoginBtn = document.getElementById('twitch-login-btn');

        // Initialize app
        async function init() {
            setupEventListeners();
            await checkSession();
            connectToServer();
        }

        async function syncWithConfigServer() {
            if (!isAdmin) return;

            try {
                // Show loading state
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = '📡 Syncing...';
                button.disabled = true;

                const response = await fetch('/api/config/sync', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to sync with config server');
                }

                const result = await response.json();
                console.log('Config sync completed:', result);

                if (result.stats) {
                    alert(`Config sync completed!\n\nGames updated: ${result.stats.gamesUpdated}\nTotal games: ${result.stats.totalGames}\nPlaylists available: ${result.stats.playlistsAvailable}`);
                } else {
                    alert('Config sync completed successfully!');
                }

                // Restore button
                button.textContent = originalText;
                button.disabled = false;
            } catch (error) {
                console.error('Config sync failed:', error);
                alert(`Config sync failed: ${error.message}`);

                // Restore button
                const button = event.target;
                button.textContent = '📡 Config Sync';
                button.disabled = false;
            }
        }



        function setupEventListeners() {
            twitchLoginBtn.addEventListener('click', handleTwitchLogin);
            logoutBtn.addEventListener('click', logout);
        }

        // Session management
        async function checkSession() {
            try {
                const response = await fetch('/api/session');
                if (response.ok) {
                    const user = await response.json();
                    setUser(user);
                }
            } catch (e) {
                console.log('No session found');
            }
        }

        function handleTwitchLogin() {
            const scope = encodeURIComponent('openid');
            window.location = `https://id.twitch.tv/oauth2/authorize?client_id=******************************&redirect_uri=https://games.xenny.news/&response_type=token&scope=${scope}&force_verify=true`;
        }

        async function handleTwitchLoginToken(token) {
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ token })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Login failed');
                }

                const user = await response.json();
                setUser(user);
                window.location.hash = '';
            } catch (e) {
                console.error('Login failed:', e);
                showError(`Login fehlgeschlagen: ${e.message}`);
            }
        }

        function setUser(user) {
            currentUser = user;
            isAdmin = user.isAdmin;

            loginSection.style.display = 'none';
            userSection.style.display = 'flex';

            let avatar = user.avatar;
            if (avatar) {
                avatar = avatar.replace('http://', 'https://');
            } else {
                avatar = `https://static-cdn.jtvnw.net/jtv_user_pictures/${user.username}-profile_image-70x70.png`;
            }

            userAvatar.onerror = function() {
                this.src = 'https://static-cdn.jtvnw.net/jtv_user_pictures/unknown-profile_image-70x70.png';
            };
            userAvatar.src = avatar;
            userName.textContent = user.display_name || user.username;

            if (user.isAdmin) {
                adminBadge.style.display = 'inline-block';
                adminPanel.style.display = 'block';
            }

            renderGames();
        }

        async function logout() {
            try {
                await fetch('/api/logout', { method: 'POST' });
            } catch (e) {
                console.error('Logout failed:', e);
            }

            currentUser = null;
            isAdmin = false;
            userSection.style.display = 'none';
            loginSection.style.display = 'block';
            adminBadge.style.display = 'none';
            adminPanel.style.display = 'none';
            renderGames();
        }

        // Socket.IO connection
        function connectToServer() {
            if (socket) socket.disconnect();

            socket = io({
                reconnection: true,
                reconnectionAttempts: MAX_RECONNECT_ATTEMPTS,
                reconnectionDelay: RECONNECT_DELAY,
                reconnectionDelayMax: 5000,
                randomizationFactor: 0.5
            });

            socket.on('connect', () => {
                console.log('Connected to server');
                reconnectAttempts = 0;
                connectionStatus.textContent = 'Verbunden';
                connectionStatus.className = 'connection-status connected';
                hideError();
            });

            socket.on('init', (data) => {
                gameData = data;
                renderGames();
            });

            socket.on('update', (data) => {
                gameData = data;
                renderGames();
            });

            socket.on('disconnect', (reason) => {
                console.log('Disconnected:', reason);
                connectionStatus.textContent = 'Verbindung getrennt';
                connectionStatus.className = 'connection-status disconnected';

                if (reason === 'io server disconnect') {
                    setTimeout(() => socket.connect(), RECONNECT_DELAY);
                }
            });

            socket.on('reconnect_attempt', (attemptNumber) => {
                reconnectAttempts = attemptNumber;
                connectionStatus.textContent = `Verbindung wird hergestellt... (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`;
            });

            socket.on('reconnect_failed', () => {
                connectionStatus.textContent = 'Verbindung fehlgeschlagen. Seite neu laden.';
                showError('Verbindung zum Server verloren. Bitte Seite neu laden.');
            });

            socket.on('connect_error', (error) => {
                console.error('Connection error:', error);
                showError(`Verbindungsfehler: ${error.message}`);
            });
        }

        // Game rendering
        function renderGames() {
            if (!gameData) {
                loading.style.display = 'block';
                dashboard.style.display = 'none';
                return;
            }

            loading.style.display = 'none';
            dashboard.style.display = 'grid';

            renderGameCategory('gespielt', gameData.games, 'Gespielt');
            renderGameCategory('wird-gespielt', gameData.games, 'Spielt gerade');
            renderGameCategory('spieleliste', gameData.games, 'Auf der Spieleliste');
        }

        function renderGameCategory(containerId, games, category) {
            const container = document.getElementById(`${containerId}-games`);
            const countElement = document.getElementById(`${containerId}-count`);

            if (!container || !countElement) return;

            container.innerHTML = '';

            const filteredGames = Object.entries(games).filter(([name, game]) => game.overlay === category);

            // Sort games based on category
            if (category === 'Auf der Spieleliste') {
                filteredGames.sort((a, b) => {
                    const scoreA = gameData.votes[a[0]]?.score || 0;
                    const scoreB = gameData.votes[b[0]]?.score || 0;
                    if (scoreA !== scoreB) return scoreB - scoreA;
                    return b[1].createdAt.localeCompare(a[1].createdAt);
                });
            } else if (category === 'Gespielt') {
                filteredGames.sort((a, b) => {
                    const dateA = b[1].movedToCurrentCategoryAt || b[1].createdAt;
                    const dateB = a[1].movedToCurrentCategoryAt || a[1].createdAt;
                    return dateA.localeCompare(dateB);
                });
            } else {
                filteredGames.sort((a, b) => b[1].createdAt.localeCompare(a[1].createdAt));
            }

            countElement.textContent = `${filteredGames.length} Spiel${filteredGames.length !== 1 ? 'e' : ''}`;

            filteredGames.forEach(([gameName, gameInfo]) => {
                const gameElement = createGameElement(gameName, gameInfo, containerId);
                container.appendChild(gameElement);
            });
        }

        function createGameElement(gameName, gameInfo, categoryId) {
            const gameDiv = document.createElement('div');
            gameDiv.className = 'game-boxart';
            gameDiv.setAttribute('data-game', gameName);
            gameDiv.draggable = isAdmin;

            const votes = gameData.votes[gameName] || { score: 0, votes: {} };
            const userVote = currentUser ? votes.votes[currentUser.username] : null;

            const img = document.createElement('img');
            img.className = 'boxart-image';
            img.alt = gameName;
            img.onerror = function() {
                this.onerror = null;
                this.src = 'https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png';
            };

            const coverUrl = gameInfo.cover_url || 'https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png';
            img.src = coverUrl.startsWith('http') ? coverUrl : 'https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png';

            // Create playlist section HTML
            const playlistSection = createPlaylistSection(gameName, gameInfo.youtube_playlist);

            gameDiv.innerHTML = `
                <div class="boxart-footer">
                    <div class="boxart-name" title="${gameName}">${gameName}</div>

                    <div class="boxart-meta">
                        ${categoryId === 'gespielt' ? `
                            <span class="boxart-status status-date">
                                ${gameInfo.movedToCurrentCategoryAt ?
                                    new Date(gameInfo.movedToCurrentCategoryAt).toLocaleDateString('de-DE', {
                                        year: 'numeric',
                                        month: '2-digit',
                                        day: '2-digit'
                                    }) :
                                    (gameInfo.createdAt ?
                                        new Date(gameInfo.createdAt).toLocaleDateString('de-DE', {
                                            year: 'numeric',
                                            month: '2-digit',
                                            day: '2-digit'
                                        }) :
                                        'Unbekannt'
                                    )
                                }
                            </span>
                        ` : `
                            <span class="boxart-status ${gameInfo.owned ? 'status-owned' : 'status-not-owned'}">
                                ${gameInfo.owned ? 'Gekauft' : 'Nicht Gekauft'}
                            </span>
                        `}

                        ${categoryId === 'spieleliste' ? `
                            <div class="boxart-votes">
                                <button class="boxart-vote-btn upvote ${userVote === 'up' ? 'active' : ''}"
                                        onclick="vote('${gameName.replace(/'/g, "\\'")}', 'up', event)"
                                        ${currentUser ? '' : 'disabled'}>
                                    ▲
                                </button>
                                <span class="boxart-score ${getScoreClass(votes.score)}">
                                    ${votes.score}
                                </span>
                                <button class="boxart-vote-btn downvote ${userVote === 'down' ? 'active' : ''}"
                                        onclick="vote('${gameName.replace(/'/g, "\\'")}', 'down', event)"
                                        ${currentUser ? '' : 'disabled'}>
                                    ▼
                                </button>
                            </div>
                        ` : playlistSection ? `
                            <div class="boxart-votes">
                                ${playlistSection}
                            </div>
                        ` : `
                            <div class="boxart-votes">
                                <span class="boxart-score ${getScoreClass(votes.score)}">
                                    ${votes.score}
                                </span>
                            </div>
                        `}
                    </div>

                    ${categoryId === 'spieleliste' && playlistSection ? `
                        ${playlistSection}
                    ` : ''}

                    ${isAdmin ? `
                    <div class="boxart-actions">
                        <select class="boxart-category-select"
                                onchange="changeCategory('${gameName.replace(/'/g, "\\'")}', this.value)">
                            <option value="Gespielt" ${gameInfo.overlay === 'Gespielt' ? 'selected' : ''}>✅ Gespielt</option>
                            <option value="Spielt gerade" ${gameInfo.overlay === 'Spielt gerade' ? 'selected' : ''}>🎯 Spielt</option>
                            <option value="Auf der Spieleliste" ${gameInfo.overlay === 'Auf der Spieleliste' ? 'selected' : ''}>📋 Liste</option>
                        </select>
                        ${categoryId !== 'gespielt' ? `
                            <button class="action-btn" onclick="toggleOwned('${gameName.replace(/'/g, "\\'")}', event)">
                                Besitz ändern
                            </button>
                        ` : ''}
                        <button class="action-btn" style="background: #f44336; color: white;"
                                onclick="removeGame('${gameName.replace(/'/g, "\\'")}', event)">
                            × Löschen
                        </button>
                        ${(categoryId !== 'spieleliste' && !playlistSection) ? `
                            <button class="action-btn playlist-btn edit" onclick="editPlaylist('${gameName.replace(/'/g, "\\'")}', event)">
                                + Playlist hinzufügen
                            </button>
                        ` : ''}
                    </div>
                    ` : ''}
                </div>
            `;

            if (isAdmin) {
                gameDiv.addEventListener('dragstart', handleDragStart);
                gameDiv.addEventListener('dragend', handleDragEnd);
            }

            gameDiv.insertBefore(img, gameDiv.firstChild);
            return gameDiv;
        }

        function createPlaylistSection(gameName, playlistUrl) {
            if (playlistUrl) {
                const youtubeLogoSvg = `
                    <svg class="youtube-logo" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                    </svg>
                `;

                return `
                    <a href="${playlistUrl}" target="_blank" class="playlist-link">
                        ${youtubeLogoSvg}
                    </a>
                    ${isAdmin ? `
                        <button class="playlist-btn edit" onclick="editPlaylist('${gameName.replace(/'/g, "\\'")}', event)" title="Playlist bearbeiten">
                            ✏️
                        </button>
                    ` : ''}
                `;
            } else if (isAdmin) {
                return `
                    <button class="playlist-btn edit" onclick="editPlaylist('${gameName.replace(/'/g, "\\'")}', event)">
                        + Playlist hinzufügen
                    </button>
                `;
            }
            return '';
        }

        // Playlist management functions
        function editPlaylist(gameName, event) {
            event.stopPropagation();
            if (!isAdmin) return;

            const gameElement = document.querySelector(`.game-boxart[data-game="${gameName}"]`);
            const playlistContainer = gameElement.querySelector('.boxart-votes') || gameElement.querySelector('.playlist-section');
            const currentUrl = gameData.games[gameName]?.youtube_playlist || '';

            playlistContainer.innerHTML = `
                <input type="text" class="playlist-input"
                       value="${currentUrl}"
                       placeholder="YouTube Playlist URL eingeben..."
                       onkeypress="handlePlaylistKeypress(event, '${gameName.replace(/'/g, "\\'")}')">
                <div class="playlist-actions">
                    <button class="playlist-btn save" onclick="savePlaylist('${gameName.replace(/'/g, "\\'")}', event)">
                        💾 Speichern
                    </button>
                    <button class="playlist-btn cancel" onclick="cancelPlaylistEdit('${gameName.replace(/'/g, "\\'")}', event)">
                        ❌ Abbrechen
                    </button>
                </div>
            `;

            const input = playlistContainer.querySelector('.playlist-input');
            input.focus();
            input.select();
        }

        function handlePlaylistKeypress(event, gameName) {
            if (event.key === 'Enter') {
                savePlaylist(gameName, event);
            } else if (event.key === 'Escape') {
                cancelPlaylistEdit(gameName, event);
            }
        }

        async function savePlaylist(gameName, event) {
            event.stopPropagation();
            if (!isAdmin) return;

            const gameElement = document.querySelector(`.game-boxart[data-game="${gameName}"]`);
            const input = gameElement.querySelector('.playlist-input');
            const playlistUrl = input.value.trim();

            try {
                const response = await fetch('/api/game/playlist', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ gameName, playlistUrl })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to update playlist');
                }

                // The server will emit an update, so we don't need to manually update the UI
            } catch (error) {
                console.error('Playlist update failed:', error);
                alert(`Playlist aktualisieren fehlgeschlagen: ${error.message}`);

                // Restore the original playlist section
                cancelPlaylistEdit(gameName, event);
            }
        }

        function cancelPlaylistEdit(gameName, event) {
            event.stopPropagation();

            const gameElement = document.querySelector(`.game-boxart[data-game="${gameName}"]`);
            const playlistContainer = gameElement.querySelector('.boxart-votes');
            const currentUrl = gameData.games[gameName]?.youtube_playlist;

            if (currentUrl) {
                playlistContainer.innerHTML = createPlaylistSection(gameName, currentUrl);
            } else {
                playlistContainer.innerHTML = `<span class="boxart-score ${getScoreClass(gameData.votes[gameName]?.score || 0)}">${gameData.votes[gameName]?.score || 0}</span>`;
            }
        }



        // Utility functions
        function getScoreClass(score) {
            if (score > 0) return 'score-positive';
            if (score < 0) return 'score-negative';
            return 'score-zero';
        }

        // Game management functions
        async function vote(gameName, voteType, event) {
            event.stopPropagation();
            if (!currentUser) return;

            try {
                const currentVotes = gameData.votes[gameName] || { score: 0, votes: {} };
                const newVoteType = currentVotes.votes[currentUser.username] === voteType ? null : voteType;

                const response = await fetch('/api/vote', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ gameName, voteType: newVoteType })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Vote failed');
                }

                // Update local vote data
                if (newVoteType === null) {
                    delete currentVotes.votes[currentUser.username];
                } else {
                    currentVotes.votes[currentUser.username] = newVoteType;
                }

                currentVotes.score = Object.values(currentVotes.votes).reduce((sum, vote) => {
                    return sum + (vote === 'up' ? 1 : -1);
                }, 0);

                // Update UI immediately
                const gameElement = document.querySelector(`.game-boxart[data-game="${gameName}"]`);
                if (gameElement) {
                    const upBtn = gameElement.querySelector('.upvote');
                    const downBtn = gameElement.querySelector('.downvote');
                    const scoreSpan = gameElement.querySelector('.boxart-score');

                    upBtn.classList.toggle('active', currentVotes.votes[currentUser.username] === 'up');
                    downBtn.classList.toggle('active', currentVotes.votes[currentUser.username] === 'down');
                    scoreSpan.textContent = currentVotes.score;
                    scoreSpan.className = `boxart-score ${getScoreClass(currentVotes.score)}`;
                }
            } catch (error) {
                console.error('Vote failed:', error);
                alert(`Abstimmung fehlgeschlagen: ${error.message}`);
            }
        }

        async function addGame() {
            if (!isAdmin) return;

            const nameInput = document.getElementById('new-game-name');
            const gameName = nameInput.value.trim();

            if (!gameName) {
                alert('Bitte Spielnamen eingeben');
                return;
            }

            try {
                await fetch('/api/games', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        game: {
                            name: gameName,
                            cover_url: '',
                            owned: false
                        }
                    })
                });

                nameInput.value = '';
            } catch (error) {
                console.error('Add game failed:', error);
                alert(`Spiel hinzufügen fehlgeschlagen: ${error.message}`);
            }
        }

        async function changeCategory(gameName, category) {
            if (!isAdmin) return;

            try {
                await fetch('/api/game/update', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        gameName,
                        updates: { overlay: category }
                    })
                });
            } catch (error) {
                console.error('Category change failed:', error);
                alert(`Kategorieänderung fehlgeschlagen: ${error.message}`);
            }
        }

        async function toggleOwned(gameName, event) {
            event.stopPropagation();
            if (!isAdmin) return;

            try {
                const currentOwned = gameData.games[gameName]?.owned || false;
                await fetch('/api/game/update', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        gameName,
                        updates: { owned: !currentOwned }
                    })
                });
            } catch (error) {
                console.error('Owned toggle failed:', error);
                alert(`Besitzstatus ändern fehlgeschlagen: ${error.message}`);
            }
        }

        async function removeGame(gameName, event) {
            event.stopPropagation();
            if (!isAdmin) return;

            if (!confirm(`Möchten Sie "${gameName}" wirklich entfernen?`)) return;

            try {
                await fetch('/api/game/remove', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ gameName })
                });
            } catch (error) {
                console.error('Remove game failed:', error);
                alert(`Spiel entfernen fehlgeschlagen: ${error.message}`);
            }
        }

        // IGDB search functions
        async function searchIGDB() {
            if (!isAdmin) return;

            const query = document.getElementById('new-game-name').value.trim();
            if (!query) return;

            try {
                const response = await fetch('/api/igdb/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query })
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Search failed');
                }

                const results = await response.json();
                displayIGDBResults(results);
            } catch (error) {
                console.error('IGDB search failed:', error);
                alert(`Suche fehlgeschlagen: ${error.message}`);
            }
        }

        function displayIGDBResults(results) {
            const container = document.getElementById('igdb-results-container');
            const resultsSection = document.getElementById('igdb-search-results');

            container.innerHTML = '';
            resultsSection.style.display = 'block';

            results.forEach(game => {
                const gameDiv = document.createElement('div');
                gameDiv.className = 'igdb-result';

                let coverUrl = game.cover_url || 'https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png';
                if (!coverUrl.startsWith('http')) {
                    coverUrl = 'https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png';
                }

                gameDiv.innerHTML = `
                    <img src="${coverUrl}"
                         alt="${game.name}"
                         onerror="this.onerror=null; this.src='https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png'">
                    <div>
                        <div>${game.name}</div>
                        <button onclick="addIGDBGame('${game.name.replace(/'/g, "\\'")}', '${coverUrl}')">
                            Hinzufügen
                        </button>
                    </div>
                `;

                container.appendChild(gameDiv);
            });
        }

        function addIGDBGame(gameName, coverUrl) {
            document.getElementById('new-game-name').value = gameName;

            if (!isAdmin) return;

            fetch('/api/games', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    game: {
                        name: gameName.trim(),
                        cover_url: coverUrl,
                        owned: false
                    }
                })
            })
            .then(response => {
                if (!response.ok) throw new Error('Failed to add game');
                document.getElementById('igdb-search-results').style.display = 'none';
            })
            .catch(error => {
                console.error('Add game failed:', error);
                alert(`Spiel hinzufügen fehlgeschlagen: ${error.message}`);
            });
        }

        // Drag and drop functions
        let draggedGame = null;

        function handleDragStart(event) {
            if (!isAdmin) {
                event.preventDefault();
                return;
            }

            draggedGame = this.getAttribute('data-game');
            this.classList.add('dragging');
            event.dataTransfer.effectAllowed = 'move';
        }

        function handleDragEnd() {
            this.classList.remove('dragging');
            draggedGame = null;
        }

        function handleDragOver(event) {
            if (!isAdmin) return;
            event.preventDefault();
            this.classList.add('drop-target');
        }

        function handleDragLeave() {
            if (!isAdmin) return;
            this.classList.remove('drop-target');
        }

        function handleDrop(event) {
            event.preventDefault();
            this.classList.remove('drop-target');

            if (!draggedGame || !isAdmin) return;

            const targetCategory = this.parentElement.getAttribute('data-category');
            changeCategory(draggedGame, targetCategory);
        }

        // Error handling
        function showError(message) {
            error.style.display = 'block';
            errorMessage.textContent = message;
            dashboard.style.display = 'none';
            loading.style.display = 'none';
        }

        function hideError() {
            error.style.display = 'none';
        }

        // Event listeners setup
        document.addEventListener('DOMContentLoaded', init);

        // Setup drag and drop for game containers
        document.querySelectorAll('.game-container').forEach(container => {
            container.addEventListener('dragover', handleDragOver);
            container.addEventListener('dragleave', handleDragLeave);
            container.addEventListener('drop', handleDrop);
        });

        // Handle Twitch login token from URL hash
        window.addEventListener('load', () => {
            const hash = window.location.hash.substring(1);
            if (hash.includes('access_token')) {
                const token = hash.split('=')[1].split('&')[0];
                handleTwitchLoginToken(token);
            }
        });
    </script>
</body>
</html>